
from fpdf import FPDF
import arabic_reshaper
from bidi.algorithm import get_display
import os

def generate_contract(model, price, buyer="العميل الكريم"):
    text = f"""
عقد بيع سيارة

تم الاتفاق بين معرض بوخليفة للسيارات (الطرف الأول)
والسيد/السيدة {buyer} (الطرف الثاني)
على بيع السيارة موديل {model} بسعر {price} ريال قطري.

شروط البيع:
- يلتزم الطرف الثاني بدفع كامل المبلغ أو الالتزام بجدول الأقساط.
- لا يجوز التصرف بالسيارة قبل سداد كامل المبلغ.

التوقيع:
الطرف الأول: __________
الطرف الثاني: __________
"""

    reshaped = arabic_reshaper.reshape(text)
    bidi_text = get_display(reshaped)

    pdf = FPDF()
    pdf.add_page()
    pdf.add_font("DejaVu", "", "DejaVuSans.ttf", uni=True)
    pdf.set_font("DejaVu", size=12)
    pdf.multi_cell(0, 10, bidi_text, align='R')

    os.makedirs("contracts", exist_ok=True)
    path = f"contracts/contract_{model}.pdf"
    pdf.output(path)
    return path
