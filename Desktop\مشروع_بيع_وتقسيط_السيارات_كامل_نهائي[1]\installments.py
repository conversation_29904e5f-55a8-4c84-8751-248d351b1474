
import sqlite3

def add_installment(car_id, due_date, amount):
    con = sqlite3.connect("cars.db")
    cur = con.cursor()
    cur.execute("INSERT INTO installments (car_id, due_date, amount) VALUES (?, ?, ?)", (car_id, due_date, amount))
    con.commit()
    con.close()

def get_installments(car_id):
    con = sqlite3.connect("cars.db")
    cur = con.cursor()
    cur.execute("SELECT id, due_date, amount, status FROM installments WHERE car_id = ?", (car_id,))
    rows = cur.fetchall()
    con.close()
    return rows

def mark_paid(installment_id):
    con = sqlite3.connect("cars.db")
    cur = con.cursor()
    cur.execute("UPDATE installments SET status = 'مدفوع' WHERE id = ?", (installment_id,))
    con.commit()
    con.close()
