
from flask import Flask, render_template, request, redirect, session, url_for, send_file
import sqlite3

from generate_contract import generate_contract
from installments import add_installment, get_installments, mark_paid
app = Flask(__name__)
app.secret_key = 'supersecret'

def check_login():
    return 'user' in session

@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form["username"]
        password = request.form["password"]
        con = sqlite3.connect("cars.db")
        cur = con.cursor()
        cur.execute("SELECT * FROM users WHERE username = ? AND password = ?", (username, password))
        user = cur.fetchone()
        con.close()
        if user:
            session['user'] = username
            return redirect(url_for('index'))
        else:
            return render_template("login.html", error="بيانات الدخول غير صحيحة")
    return render_template("login.html", error=None)

@app.route("/logout")
def logout():
    session.pop('user', None)
    return redirect("/login")

@app.route("/", methods=["GET", "POST"])

@app.route("/", methods=["GET", "POST"])
def index():
    if not check_login():
        return redirect("/login")
    con = sqlite3.connect("cars.db")
    cur = con.cursor()
    if request.method == "POST":
        model = request.form["model"]
        price = request.form["price"]
        buyer = request.form.get("buyer", "العميل الكريم")
        cur.execute("INSERT INTO cars (model, price) VALUES (?, ?)", (model, price))
        con.commit()
        contract_path = generate_contract(model, price, buyer)
        con.close()
        return send_file(contract_path, as_attachment=True)
    cur.execute("SELECT * FROM cars")
    cars = cur.fetchall()
    con.close()
    return render_template("index.html", cars=cars)
def index():
    if not check_login():
        return redirect("/login")
    con = sqlite3.connect("cars.db")
    cur = con.cursor()
    if request.method == "POST":
        model = request.form["model"]
        price = request.form["price"]
        cur.execute("INSERT INTO cars (model, price) VALUES (?, ?)", (model, price))
        con.commit()
    cur.execute("SELECT * FROM cars")
    cars = cur.fetchall()
    con.close()
    return render_template("index.html", cars=cars)

if __name__ == "__main__":
    app.run(debug=True)


@app.route("/installments", methods=["GET"])
def installments_page():
    if not check_login():
        return redirect("/login")
    return render_template("installments.html", installments=None)

@app.route("/installments/add", methods=["POST"])
def add_inst():
    if not check_login():
        return redirect("/login")
    car_id = request.form["car_id"]
    due_date = request.form["due_date"]
    amount = request.form["amount"]
    add_installment(car_id, due_date, amount)
    return redirect("/installments")

@app.route("/installments/view", methods=["GET"])
def view_inst():
    if not check_login():
        return redirect("/login")
    car_id = request.args["car_id"]
    inst = get_installments(car_id)
    return render_template("installments.html", installments=inst)

@app.route("/installments/pay", methods=["POST"])
def pay_inst():
    if not check_login():
        return redirect("/login")
    inst_id = request.form["installment_id"]
    car_id = request.form["car_id"]
    mark_paid(inst_id)
    return redirect(f"/installments/view?car_id={car_id}")


@app.route("/dashboard")
def dashboard():
    if not check_login():
        return redirect("/login")
    con = sqlite3.connect("cars.db")
    cur = con.cursor()
    cur.execute("SELECT model, COUNT(*) FROM cars GROUP BY model")
    results = cur.fetchall()
    con.close()
    labels = [r[0] for r in results]
    data = [r[1] for r in results]
    return render_template("dashboard.html", labels=labels, data=data)


@app.route("/archive")
def archive():
    if not check_login():
        return redirect("/login")
    query = request.args.get("query", "")
    con = sqlite3.connect("cars.db")
    cur = con.cursor()
    if query:
        cur.execute("SELECT model, price FROM cars WHERE model LIKE ?", ('%' + query + '%',))
    else:
        cur.execute("SELECT model, price FROM cars")
    cars = cur.fetchall()
    con.close()
    return render_template("archive.html", cars=cars)
