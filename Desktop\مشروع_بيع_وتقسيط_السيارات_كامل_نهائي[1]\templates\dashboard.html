
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>لوحة تحكم</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h2>لوحة التحكم</h2>
    <canvas id="carsChart" width="400" height="200"></canvas>

    <script>
        const ctx = document.getElementById('carsChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {{ labels | safe }},
                datasets: [{
                    label: 'عدد السيارات حسب الموديل',
                    data: {{ data | safe }},
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });
    </script>
</body>
</html>
