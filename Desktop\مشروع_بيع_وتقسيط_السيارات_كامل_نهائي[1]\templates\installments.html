
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة الأقساط</title>
</head>
<body>
    <h2>إضافة قسط جديد</h2>
    <form method="POST" action="/installments/add">
        <label>رقم السيارة:</label>
        <input type="number" name="car_id" required><br>
        <label>تاريخ الاستحقاق:</label>
        <input type="date" name="due_date" required><br>
        <label>المبلغ:</label>
        <input type="number" name="amount" required><br>
        <button type="submit">إضافة القسط</button>
    </form>

    <h2>عرض الأقساط</h2>
    <form method="GET" action="/installments/view">
        <label>رقم السيارة:</label>
        <input type="number" name="car_id" required>
        <button type="submit">عرض</button>
    </form>

    {% if installments %}
        <table border="1">
            <tr><th>التاريخ</th><th>المبلغ</th><th>الحالة</th><th>إجراء</th></tr>
            {% for inst in installments %}
            <tr>
                <td>{{ inst[1] }}</td>
                <td>{{ inst[2] }}</td>
                <td>{{ inst[3] }}</td>
                <td>
                    {% if inst[3] != "مدفوع" %}
                    <form method="POST" action="/installments/pay">
                        <input type="hidden" name="installment_id" value="{{ inst[0] }}">
                        <input type="hidden" name="car_id" value="{{ request.args.get('car_id') }}">
                        <button type="submit">تم السداد</button>
                    </form>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </table>
    {% endif %}
</body>
</html>
